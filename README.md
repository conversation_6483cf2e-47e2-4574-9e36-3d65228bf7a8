# ESP8266 Web Server với mDNS

Chương trình ESP8266 tạo web server với hỗ trợ mDNS để truy cập qua tên miền thay vì địa chỉ IP.

## 🔧 Cấu hình

### 1. Cập nhật thông tin Wi-Fi

Mở file `src/main.cpp` và thay đổi thông tin Wi-Fi:

```cpp
const char* ssid = "TEN_WIFI_CUA_BAN";        // Thay bằng tên Wi-Fi
const char* password = "MAT_KHAU_WIFI_CUA_BAN"; // Thay bằng mật khẩu Wi-Fi
```

### 2. Tùy chỉnh tên miền (tùy chọn)

Có thể thay đổi tên miền mDNS:

```cpp
const char* mdnsName = "esp32";  // Truy cập qua http://esp32.local
```

## 🚀 Cách chạy

1. **Upload code lên ESP32:**
   ```bash
   pio run --target upload
   ```

2. **Mở Serial Monitor:**
   ```bash
   pio device monitor
   ```

3. **Xem thông tin kết nối:**
   - Địa chỉ IP sẽ hiển thị trên Serial Monitor
   - Ví dụ: `*************`

## 🌐 Cách truy cập

### Từ máy tính (Windows/Mac/Linux):

1. **Qua mDNS (khuyến nghị):**
   - Mở trình duyệt
   - Nhập: `http://esp32.local`

2. **Qua địa chỉ IP:**
   - Nhập địa chỉ IP hiển thị trên Serial Monitor
   - Ví dụ: `http://*************`

### Từ điện thoại:

#### iPhone/iPad (iOS):
- Hỗ trợ mDNS tự nhiên
- Mở Safari và nhập: `http://esp32.local`

#### Android:
- **Cách 1:** Sử dụng địa chỉ IP trực tiếp
- **Cách 2:** Cài app hỗ trợ mDNS:
  - **BonjourBrowser** (miễn phí)
  - **Network Discovery** 
  - **Fing** (có tính năng mDNS)

#### Ứng dụng Android khuyến nghị:

1. **BonjourBrowser:**
   - Tải từ Google Play Store
   - Mở app → tìm "esp32" → nhấn để truy cập

2. **Chrome với IP:**
   - Mở Chrome
   - Nhập địa chỉ IP: `http://*************`

## 🔍 Khắc phục sự cố

### Không truy cập được qua mDNS:

1. **Kiểm tra mạng:**
   - Đảm bảo thiết bị và ESP8266 cùng mạng Wi-Fi
   - Thử ping: `ping esp32.local`

2. **Thử địa chỉ IP:**
   - Sử dụng IP hiển thị trên Serial Monitor

3. **Khởi động lại:**
   - Reset ESP8266
   - Khởi động lại router Wi-Fi

### Lỗi kết nối Wi-Fi:

1. **Kiểm tra thông tin:**
   - SSID và password chính xác
   - Wi-Fi 2.4GHz (ESP32 không hỗ trợ 5GHz)

2. **Kiểm tra Serial Monitor:**
   - Xem thông báo lỗi chi tiết

## 📱 Giao diện Web

Trang web hiển thị:
- Thông báo chào mừng đến với ESP8266
- Địa chỉ IP hiện tại
- Tên miền mDNS
- Thông tin Wi-Fi
- Cường độ tín hiệu
- Thời gian hoạt động

## 🛠️ Tùy chỉnh

### Thay đổi giao diện:
- Chỉnh sửa hàm `handleRoot()` trong `main.cpp`
- Thêm CSS/HTML tùy ý

### Thêm trang mới:
```cpp
server.on("/about", [](){
  server.send(200, "text/html", "<h1>Trang giới thiệu</h1>");
});
```

## 📋 Thông tin kỹ thuật

- **Platform:** ESP8266 (espressif8266)
- **Board:** NodeMCU v2 (ESP8266)
- **Framework:** Arduino
- **Thư viện sử dụng:**
  - ESP8266WiFi.h (kết nối Wi-Fi)
  - ESP8266mDNS.h (mDNS service)
  - ESP8266WebServer.h (HTTP server)
- **Port:** 80 (HTTP)
- **Baud rate:** 115200

## 🎯 Tính năng

- ✅ Kết nối Wi-Fi tự động
- ✅ Web server HTTP
- ✅ mDNS (truy cập qua tên miền)
- ✅ Giao diện responsive
- ✅ Hiển thị thông tin hệ thống
- ✅ Xử lý lỗi 404
- ✅ Serial Monitor chi tiết
