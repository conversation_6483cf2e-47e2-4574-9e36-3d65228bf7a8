#include <Arduino.h>
#include <ESP8266WiFi.h>
#include <ESP8266mDNS.h>
#include <ESP8266WebServer.h>

// Thông tin Wi-Fi - Thay đổi theo mạng của bạn
const char* ssid = "YOUR_WIFI_SSID";        // Thay bằng tên Wi-Fi của bạn
const char* password = "YOUR_WIFI_PASSWORD"; // Thay bằng mật khẩu Wi-Fi của bạn

// Tên miền mDNS - có thể thay đổi
const char* mdnsName = "esp32";  // Truy cập qua http://esp32.local

// Tạo web server trên port 80
ESP8266WebServer server(80);

// Hàm xử lý trang chủ
void handleRoot() {
  String html = "<!DOCTYPE html>";
  html += "<html lang='vi'>";
  html += "<head>";
  html += "<meta charset='UTF-8'>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
  html += "<title>ESP32 Web Server</title>";
  html += "<style>";
  html += "body { font-family: Arial, sans-serif; text-align: center; margin: 50px; background-color: #f0f0f0; }";
  html += ".container { background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto; }";
  html += "h1 { color: #333; margin-bottom: 20px; }";
  html += ".info { background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }";
  html += ".status { color: #28a745; font-weight: bold; }";
  html += "</style>";
  html += "</head>";
  html += "<body>";
  html += "<div class='container'>";
  html += "<h1>🎉 Chào mừng đến với ESP8266! 🎉</h1>";
  html += "<div class='info'>";
  html += "<p><strong>Trạng thái:</strong> <span class='status'>Đang hoạt động</span></p>";
  html += "<p><strong>Địa chỉ IP:</strong> " + WiFi.localIP().toString() + "</p>";
  html += "<p><strong>Tên miền:</strong> http://" + String(mdnsName) + ".local</p>";
  html += "<p><strong>SSID:</strong> " + String(ssid) + "</p>";
  html += "<p><strong>Cường độ tín hiệu:</strong> " + String(WiFi.RSSI()) + " dBm</p>";
  html += "</div>";
  html += "<p>ESP8266 đã kết nối thành công và đang chạy web server!</p>";
  html += "<p><em>Thời gian hoạt động: " + String(millis() / 1000) + " giây</em></p>";
  html += "</div>";
  html += "</body>";
  html += "</html>";

  server.send(200, "text/html", html);
}

// Hàm xử lý trang không tìm thấy
void handleNotFound() {
  String message = "Trang không tìm thấy!\n\n";
  message += "URI: " + server.uri() + "\n";
  message += "Method: ";
  message += (server.method() == HTTP_GET) ? "GET" : "POST";
  message += "\n";
  message += "Arguments: " + String(server.args()) + "\n";

  for (uint8_t i = 0; i < server.args(); i++) {
    message += " " + server.argName(i) + ": " + server.arg(i) + "\n";
  }

  server.send(404, "text/plain", message);
}

void setup() {
  // Khởi tạo Serial Monitor
  Serial.begin(115200);
  Serial.println();
  Serial.println("=== ESP8266 Web Server với mDNS ===");

  // Kết nối Wi-Fi
  Serial.print("Đang kết nối Wi-Fi");
  WiFi.begin(ssid, password);

  // Chờ kết nối Wi-Fi
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }

  Serial.println();
  Serial.println("✅ Kết nối Wi-Fi thành công!");
  Serial.print("📶 SSID: ");
  Serial.println(ssid);
  Serial.print("🌐 Địa chỉ IP: ");
  Serial.println(WiFi.localIP());
  Serial.print("📡 Cường độ tín hiệu: ");
  Serial.print(WiFi.RSSI());
  Serial.println(" dBm");

  // Khởi tạo mDNS
  if (MDNS.begin(mdnsName)) {
    Serial.println("✅ mDNS khởi tạo thành công!");
    Serial.print("🔗 Truy cập qua: http://");
    Serial.print(mdnsName);
    Serial.println(".local");
  } else {
    Serial.println("❌ Lỗi khởi tạo mDNS!");
  }

  // Cấu hình các route cho web server
  server.on("/", handleRoot);
  server.onNotFound(handleNotFound);

  // Khởi động web server
  server.begin();
  Serial.println("✅ Web server đã khởi động!");
  Serial.println("📋 Các cách truy cập:");
  Serial.print("   - http://");
  Serial.println(WiFi.localIP());
  Serial.print("   - http://");
  Serial.print(mdnsName);
  Serial.println(".local");
  Serial.println("==========================================");
}

void loop() {
  // Xử lý các request từ client
  server.handleClient();

  // Cập nhật mDNS (cần thiết cho ESP8266)
  MDNS.update();

  // Delay nhỏ để tránh watchdog reset
  delay(2);
}